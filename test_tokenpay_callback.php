<?php
echo "=== 测试TokenPay回调处理 ===\n";

// 模拟TokenPay发送的回调数据
$testData = [
    "ActualAmount" => "20",
    "Amount" => "7.72", 
    "BaseCurrency" => "CNY",
    "BlockChainName" => "TRON",
    "BlockTransactionId" => "1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31",
    "Currency" => "TRX",
    "CurrencyName" => "TRX", 
    "FromAddress" => "TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS",
    "Id" => "689f0e41-c5ee-b4c5-0055-249046dc733c",
    "IsDynamicAmount" => 0,
    "OrderUserKey" => "1035712030",
    "OutOrderId" => "20250815103855825",
    "PayAmount" => "7.72",
    "PayTime" => "2025-08-15 10:41:18",
    "Signature" => "713e605f6ecdddb05fdb00708a32d229",
    "Status" => 1,
    "ToAddress" => "TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"
];

// 模拟POST请求到回调处理器
$postData = json_encode($testData);

// 使用curl发送测试请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/epay/tokenpay_notify.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($postData)
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
echo "响应内容: $response\n";

if ($httpCode === 200) {
    echo "✓ 回调处理成功\n";
} else {
    echo "✗ 回调处理失败\n";
}

echo "\n=== 测试完成 ===\n";
?>
