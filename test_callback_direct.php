<?php
echo "=== 直接测试TokenPay回调处理逻辑 ===\n";

// 模拟环境变量
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';

// 模拟TokenPay发送的回调数据
$testData = [
    "ActualAmount" => "20",
    "Amount" => "7.72", 
    "BaseCurrency" => "CNY",
    "BlockChainName" => "TRON",
    "BlockTransactionId" => "1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31",
    "Currency" => "TRX",
    "CurrencyName" => "TRX", 
    "FromAddress" => "TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS",
    "Id" => "689f0e41-c5ee-b4c5-0055-249046dc733c",
    "IsDynamicAmount" => 0,
    "OrderUserKey" => "1035712030",
    "OutOrderId" => "20250815103855825",
    "PayAmount" => "7.72",
    "PayTime" => "2025-08-15 10:41:18",
    "Signature" => "713e605f6ecdddb05fdb00708a32d229",
    "Status" => 1,
    "ToAddress" => "TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"
];

$rawInput = json_encode($testData);

// 重定向php://input
class TestInputWrapper {
    public $context;
    private $position = 0;
    private static $data = '';
    
    public static function setData($data) {
        self::$data = $data;
    }
    
    public function stream_open($path, $mode, $options, &$opened_path) {
        if ($path === 'php://input') {
            $this->position = 0;
            return true;
        }
        return false;
    }
    
    public function stream_read($count) {
        $ret = substr(self::$data, $this->position, $count);
        $this->position += strlen($ret);
        return $ret;
    }
    
    public function stream_eof() {
        return $this->position >= strlen(self::$data);
    }
    
    public function stream_stat() {
        return array();
    }
}

// 注册自定义流包装器
if (in_array('php', stream_get_wrappers())) {
    stream_wrapper_unregister('php');
}
stream_wrapper_register('php', 'TestInputWrapper');
TestInputWrapper::setData($rawInput);

echo "模拟回调数据: " . $rawInput . "\n\n";

// 捕获输出
ob_start();

try {
    // 切换到epay目录
    chdir('epay');
    // 包含回调处理文件
    include 'tokenpay_notify.php';
} catch (Exception $e) {
    echo "处理异常: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "处理错误: " . $e->getMessage() . "\n";
} finally {
    // 切换回原目录
    chdir('..');
}

$output = ob_get_clean();
echo "回调处理输出: $output\n";

// 检查回调日志
$logFile = 'epay/tokenpay_callback.log';
if (file_exists($logFile)) {
    echo "\n=== 回调处理日志 ===\n";
    $logContent = file_get_contents($logFile);
    $lines = explode("\n", $logContent);
    // 显示最后20行日志
    $recentLines = array_slice($lines, -20);
    foreach ($recentLines as $line) {
        if (trim($line)) {
            echo $line . "\n";
        }
    }
} else {
    echo "\n回调日志文件不存在\n";
}

echo "\n=== 测试完成 ===\n";
?>
