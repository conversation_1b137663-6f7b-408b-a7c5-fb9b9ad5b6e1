<?php include('head.php'); include('footer.php');$uid=$userrow['uid']; ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单页面 - <?=$conf['sitename'];?></title>
    <!-- Element UI 样式 -->
    <link rel="stylesheet" href="../sxdk/element/index.css">
    <script src="./dk/jquery.js"></script>
    <script src="./dk/vue.js"></script>
    <script src="./dk/vue-resource.min.js"></script>
    <script src="../sxdk/element/index.js"></script>
    <script src="layer/3.1.1/layer.js"></script>
    <style>
        .payment-container { max-width: 2400px; margin: 10px auto; padding: 10px; }
        .user-info-card { margin-bottom: 20px; }
        .payment-form { margin: 20px 0; }
        .rules-alert { margin: 15px 0; }
        .order-table { margin-top: 20px; }
        .el-descriptions__body { background: #f5f7fa; }
        .payment-methods .el-radio-group { margin-bottom: 15px; }

        /* 支付方式按钮对齐样式 */
        .pay-type-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }
        .pay-type-group .el-radio {
            margin: 0; /* 重置默认间距，使用 gap 统一控制 */
            flex: 1;
            min-width: 120px;
        }

        /* 推荐标签样式 */
        .recommend-tag {
            background: #F56C6C;
            color: white;
            font-size: 10px;
            padding: 1px 4px;
            border-radius: 8px;
            margin-left: 3px;
            font-weight: normal;
        }

        /* 响应式弹窗样式 */
        @media (max-width: 768px) {
            /* 移动端弹窗全屏显示 */
            .el-dialog {
                width: 95% !important;
                margin: 5vh auto !important;
                max-height: 90vh;
            }

            .el-dialog__body {
                padding: 15px !important;
                max-height: 70vh;
                overflow-y: auto;
            }

            /* 移动端支付方式按钮垂直排列 */
            .pay-type-group {
                flex-direction: column;
                gap: 8px;
            }

            .pay-type-group .el-radio {
                width: 100%;
                min-width: auto;
                justify-content: center;
            }

            /* 移动端字体调整 */
            .el-dialog__title {
                font-size: 16px !important;
            }

            .el-form-item__label {
                font-size: 14px !important;
            }

            /* 移动端按钮调整 */
            .dialog-footer .el-button {
                width: 45%;
                margin: 0 2.5%;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            /* 平板端弹窗 */
            .el-dialog {
                width: 60% !important;
            }

            .pay-type-group .el-radio {
                flex: 0 0 calc(50% - 5px);
            }
        }

        @media (min-width: 1025px) {
            /* 桌面端弹窗 */
            .el-dialog {
                width: 40% !important;
                min-width: 400px;
            }
        }

        /* 支付金额显示优化 */
        .payment-amount {
            text-align: center;
            padding: 10px;
            background: #f5f7fa;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .payment-amount h3 {
            margin: 0;
            color: #409EFF;
            font-size: 24px;
        }

        @media (max-width: 768px) {
            .payment-amount h3 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
<div id="app">
    <!-- 隐藏的支付表单 -->
    <form ref="payForm" action="/epay/epay.php" method="post" style="display: none;">
        <input type="hidden" name="out_trade_no" :value="out_trade_no">
        <input type="hidden" name="type" :value="payType">
    </form>

    <!-- TokenPay 引导（如需跳外部链接） -->
    <form ref="tokenPayForm" action="" method="get" style="display:none;">
        <input type="hidden" name="order_no" :value="out_trade_no">
        <input type="hidden" name="amount" :value="money">
        <input type="hidden" name="uid" value="<?=$userrow['uid'];?>">
    </form>

    <div class="payment-container">
        <!-- 用户信息 -->
        <el-card class="user-info-card">
            <div slot="header" class="clearfix">
                <span>账户信息</span>
            </div>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="欢迎语"><?=$conf['sitename'];?> 欢迎您！</el-descriptions-item>
                <el-descriptions-item label="昵称"><span style="color: #F56C6C;">
                <?=$userrow['name'];?></span></el-descriptions-item>
                <el-descriptions-item label="账号"><span style="color: #F56C6C;">
                <?=$userrow['user'];?></span></el-descriptions-item>
                <el-descriptions-item label="余额"><span style="color: #67C23A;">
                <?=$userrow['money'];?></span></el-descriptions-item>
                <el-descriptions-item label="累计余额"><span style="color: #67C23A;">
                <?=$userrow['zcz'];?></span></el-descriptions-item>
            </el-descriptions>
        </el-card>

        <!-- 在线充值 -->
        <el-card class="payment-card">
            <div slot="header" class="clearfix">
                <span>在线充值</span>
            </div>
            <el-form class="payment-form" label-width="80px">
                <el-form-item label="充值金额">
                    <el-input v-model="money" placeholder="请输入充值金额" style="width: 150px"></el-input>
                    <el-button type="primary" @click="pay" style="margin-left: 10px" :loading="isSubmitting" :disabled="isSubmitting">立即充值</el-button>
                    <div style="margin-top: 8px;">
                        <el-button v-for="amt in quickAmounts" :key="amt" size="mini" @click="money = String(amt)">{{amt}}</el-button>
                    </div>
                    <div v-if="Number(money) > 0" style="margin-top: 8px; font-size: 12px; color: #606266;">
                        传统：赠送 ¥{{ formatAmount(bonusTraditional) }}，到账 ¥{{ formatAmount(totalTraditional) }}；
                        加密货币：赠送 ¥{{ formatAmount(bonusCrypto) }}，到账 ¥{{ formatAmount(totalCrypto) }}
                    </div>
                </el-form-item>
            </el-form>
            
            <el-alert class="rules-alert" title="充值规则" type="success" :closable="false">
                <ul style="margin: 5px 0 0 20px">
                    <li>50元以上送10%金额（加密货币充值送12%）</li>
                    <li>100元以上送15%金额（加密货币充值送17%）</li>
                    <li>200元以上送20%金额（加密货币充值送22%）</li>
                    <li>300元以上送25%金额（加密货币充值送27%）</li>
                    <li>500元以上送30%金额（加密货币充值送32%）</li>
                </ul>
                <div style="margin-top: 10px; color: #409eff; font-size: 13px;">
                    提示：加密货币充值在所有档位比传统支付多送2%
                </div>
                <div style="margin-top: 5px">密接用户可联系上级享受专属优惠！</div>
            </el-alert>
        </el-card>

        <!-- 卡密充值 -->
        <el-card class="payment-card">
            <div slot="header" class="clearfix">
                <span>卡密充值</span>
            </div>
            <el-form class="payment-form" label-width="80px">
                <el-form-item label="充值卡密">
                    <el-input v-model="km" placeholder="请输入充值卡密" style="width: 150px"></el-input>
                    <el-button type="primary" @click="paycard" style="margin-left: 10px">立即充值</el-button>
                </el-form-item>
            </el-form>
            <div class="tips">
                <el-alert type="warning" :closable="false">
                    <div>1. 正常情况下请联系上家进行充值</div>
                    <div>2. 充值卡不定时作为福利发放</div>
                    <div>3. 一切安好！</div>
                </el-alert>
            </div>
        </el-card>

        <!-- 卡密查询 -->
        <el-card class="payment-card">
            <div slot="header" class="clearfix">
                <span>充值卡查询</span>
            </div>
            <el-form class="payment-form" label-width="80px">
                <el-form-item label="查询卡密">
                    <el-input v-model="querykm" placeholder="请输入查询卡密" style="width: 150px"></el-input>
                    <el-button type="primary" @click="querycard" style="margin-left: 10px">立即查询</el-button>
                </el-form-item>
            </el-form>
            <el-descriptions :column="1" border>
                <el-descriptions-item label="充值卡号">{{kminfo.content || '暂无数据'}}</el-descriptions-item>
                <el-descriptions-item label="卡密金额">¥ {{kminfo.money || '0'}}</el-descriptions-item>
                <el-descriptions-item label="卡密状态">
                    <el-tag :type="kminfo.status == 1 ? 'danger' : 'success'">
                        {{kminfo.status == 1 ? '已使用' : '未使用'}}
                    </el-tag>
                </el-descriptions-item>
            </el-descriptions>
        </el-card>

        <!-- 支付方式弹窗（响应式自适应） -->
        <el-dialog
            title="选择支付方式"
            :visible.sync="payDialogVisible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            @close="payDialogVisible = false"
            class="payment-dialog">
            <div class="payment-amount">
                <h3>¥{{money}}</h3>
                <p style="margin: 5px 0 0 0; color: #909399; font-size: 12px;">请选择支付方式</p>
            </div>

            <el-form>
                <div class="payment-methods">
                    <el-radio-group v-model="payType" class="pay-type-group">
                    <?php if(isset($conf['tokenpay_enabled']) && intval($conf['tokenpay_enabled']) === 1): ?>
                        <el-radio label="tokenpay_trx" border>
                            <i class="el-icon-wallet"></i> TRX
                            <span class="recommend-tag">推荐</span>
                        </el-radio>
                        <el-radio label="tokenpay_usdt" border>
                            <i class="el-icon-coin"></i> USDT
                        </el-radio>
                    <?php endif; ?>
                    <?php if($conf['is_alipay']==1): ?>
                        <el-radio label="alipay" border>
                            <i class="el-icon-wallet"></i> 支付宝
                        </el-radio>
                    <?php endif; ?>
                    <?php if($conf['is_qqpay']==1): ?>
                        <el-radio label="qqpay" border>
                            <i class="el-icon-chat-dot-round"></i> QQ支付
                        </el-radio>
                    <?php endif; ?>
                    <?php if($conf['is_wxpay']==1): ?>
                        <el-radio label="wxpay" border>
                            <i class="el-icon-wechat"></i> 微信支付
                        </el-radio>
                    <?php endif; ?>
                    </el-radio-group>
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="payDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitPay" :disabled="!payType">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 成功订单 -->
        <el-card class="order-table">
            <div slot="header" class="clearfix">
                <span>支付记录</span>
            </div>
            <el-table 
                :data="orders" 
                stripe 
                style="width: 100%"
                v-loading="loading"
                element-loading-text="数据加载中..."
                element-loading-spinner="el-icon-loading">
                <el-table-column prop="oid" label="ID" width="80"></el-table-column>
                <el-table-column prop="out_trade_no" label="订单号"></el-table-column>
                <el-table-column prop="type" label="类型" width="100"></el-table-column>
                <el-table-column prop="uid" label="用户UID" width="100"></el-table-column>
                <el-table-column prop="name" label="名称"></el-table-column>
                <el-table-column prop="money" label="金额" width="100"></el-table-column>
                <el-table-column prop="addtime" label="创建时间" width="160"></el-table-column>
                <el-table-column prop="endtime" label="支付时间" width="160"></el-table-column>
                <el-table-column label="状态" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.status == 1 ? 'success' : 'danger'">
                            {{scope.row.status == 1 ? '已支付' : '未支付'}}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
            
            <!-- 分页组件 -->
            <div class="pagination-container">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[25, 50, 100, 500]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="Number(total)">
                </el-pagination>
            </div>
        </el-card>
    </div>
</div>

<script>
const chargeVm = new Vue({
  el: '#app',
  data: {
    money: '',
    km: '',
    querykm: '',
    kminfo: {
      content: '请先查询',
      money: '0',
      status: '',
    },
    out_trade_no: '',
    payDialogVisible: false,
      payType: 'tokenpay_trx',
    currentPage: 1,
    pageSize: 25,
    total: 10,
    loading: false,
      orders: [],
      // 新增：防二次点击与快捷金额
      isSubmitting: false,
      quickAmounts: [20, 50, 100, 200, 500]
  },
  methods: {
    formatAmount(val){
      const n = Number(val || 0);
      return n.toFixed(2);
    },
    computeBonusTraditional(money){
      const m = Number(money || 0);
      if(m >= 500) return m * 0.30;
      if(m >= 300) return m * 0.25;
      if(m >= 200) return m * 0.20;
      if(m >= 100) return m * 0.15;
      if(m >= 50) return m * 0.10;
      return 0;
    },
    computeBonusCrypto(money){
      // 比传统多 2%
      const m = Number(money || 0);
      if(m >= 500) return m * 0.32;
      if(m >= 300) return m * 0.27;
      if(m >= 200) return m * 0.22;
      if(m >= 100) return m * 0.17;
      if(m >= 50) return m * 0.12;
      return 0;
    },
    pay: function () {
      if(!this.money || isNaN(this.money)){
        this.$message.error('请输入有效的充值金额');
        return;
      }
      if(this.isSubmitting) return;
      this.isSubmitting = true;
      const loading = this.$loading({ lock: true, text: '正在创建订单...', spinner: 'el-icon-loading', background: 'rgba(0,0,0,0.7)' });
      this.$http.post('/apisub.php?act=pay', { money: this.money }, { emulateJSON: true })
        .then((data) => {
          loading.close();
          this.isSubmitting = false;
          if (data.data.code == 1) {
            this.out_trade_no = data.data.out_trade_no;
            this.payDialogVisible = true;
          } else {
            this.$message.error(data.data.msg || '创建订单失败');
          }
        })
        .catch((err) => {
          loading.close();
          this.isSubmitting = false;
          this.$message.error('网络异常，创建订单失败');
        })
    },
    submitPay: function() {
      if(!this.payType) {
        this.$message.error('请选择支付方式');
        return;
      }
      
      console.log('=== TokenPay 支付调试开始 ===');
      console.log('支付方式:', this.payType);
      console.log('订单号:', this.out_trade_no);
      console.log('金额:', this.money);
      
      if(this.payType === 'tokenpay_usdt' || this.payType === 'tokenpay_trx'){
        // 确定币种
        const currency = this.payType === 'tokenpay_usdt' ? 'USDT_TRC20' : 'TRX';
        console.log('币种:', currency);
        
        // 显示loading
        const loading = this.$loading({
          lock: true,
          text: '正在创建TokenPay订单...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        
        // 调用后端 API 创建 TokenPay 订单并跳转
        console.log('开始调用 TokenPay API...');
        this.$http.post('/apisub.php?act=tokenpay_create', {
          out_trade_no: this.out_trade_no,
          money: this.money,
          currency: currency
        }, { emulateJSON: true })
          .then((res) => {
            loading.close();
            console.log('TokenPay API 响应:', res);
            console.log('响应数据:', res.data);
            
            if(res.data.code === 1){
              const paymentUrl = res.data.data.paymentUrl;
              console.log('支付链接:', paymentUrl);
              
              // 关闭弹窗
              this.payDialogVisible = false;
              
              // 显示跳转提示
              this.$message({
                message: '正在跳转到TokenPay支付页面...',
                type: 'success',
                duration: 2000
              });
              
              // 延迟一点时间让用户看到消息，然后跳转
              setTimeout(() => {
                console.log('开始跳转到:', paymentUrl);
                try {
                  // 跳转到 TokenPay 收银台
                  window.location.href = paymentUrl;
                } catch (error) {
                  console.error('跳转失败:', error);
                  this.$message.error('页面跳转失败: ' + error.message);
                }
              }, 1000);
              
            } else {
              console.error('TokenPay 订单创建失败:', res.data.msg);
              this.$message.error(res.data.msg || 'TokenPay 订单创建失败');
            }
          })
          .catch((err) => {
            loading.close();
            console.error('TokenPay API 调用失败:', err);
            console.error('错误详情:', {
              message: err.message,
              status: err.status,
              statusText: err.statusText,
              data: err.data
            });
            
            let errorMsg = 'TokenPay 订单创建失败';
            if (err.status) {
              errorMsg += ` (HTTP ${err.status})`;
            }
            if (err.message) {
              errorMsg += ': ' + err.message;
            }
            
            this.$message.error(errorMsg);
          });
        return;
      }
      
      // 传统支付方式
      this.$refs.payForm.submit();
      this.payDialogVisible = false;
    },
    paycard: function () {
      var load = layer.load(2)
      this.$http.post('/km.php?act=paykm', { content: this.km }, { emulateJSON: true })
        .then(function (data) {
          layer.close(load)
          if (data.data.code == -1) {
            layer.msg(data.data.msg, { icon: 2, time: 2000, shift: 3 })
          } else {
            layer.msg(data.data.msg, { icon: 1, time: 2000, shift: 1 })
            setTimeout(() => location.reload(), 1000)
          }
        })
    },
    querycard: function () {
      var load = layer.load(2)
      this.$http.post('/km.php?act=querykm', { content: this.querykm }, { emulateJSON: true })
        .then(function (data) {
          layer.close(load)
          if (data.data.code == 1) {
            this.kminfo = data.data.data[0]
            layer.msg(data.data.msg, { icon: 1, time: 2000, anim: 3 })
          } else {
            layer.msg(data.data.msg, { icon: 2, time: 2000, anim: 1 })
          }
        })
    },
    handleSizeChange(size){ this.pageSize = size; this.fetchOrders(); },
    handleCurrentChange(page){ this.currentPage = page; this.fetchOrders(); },
    fetchOrders(){
      this.loading = true;
      this.$http.post('/apisub.php?act=paylist', { page: this.currentPage, pageSize: this.pageSize }, { emulateJSON: true })
        .then((res)=>{
        this.loading = false;
          if(res.data.code===1){
            this.orders = res.data.data;
            this.total = Number(res.data.total || 0);
            this.currentPage = Number(res.data.current_page || this.currentPage);
          }
        })
    }
  },
  computed: {
    bonusTraditional(){ return this.computeBonusTraditional(this.money); },
    totalTraditional(){ return Number(this.money || 0) + this.bonusTraditional; },
    bonusCrypto(){ return this.computeBonusCrypto(this.money); },
    totalCrypto(){ return Number(this.money || 0) + this.bonusCrypto; }
  },
  mounted(){ this.fetchOrders(); }
})
</script>
</body>
</html>