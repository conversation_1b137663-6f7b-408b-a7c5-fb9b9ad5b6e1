<?php
echo "=== 测试修复后的TokenPay回调 ===\n";

// 模拟TokenPay发送的回调数据
$testData = [
    "ActualAmount" => "20",
    "Amount" => "7.72", 
    "BaseCurrency" => "CNY",
    "BlockChainName" => "TRON",
    "BlockTransactionId" => "1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31",
    "Currency" => "TRX",
    "CurrencyName" => "TRX", 
    "FromAddress" => "TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS",
    "Id" => "689f0e41-c5ee-b4c5-0055-249046dc733c",
    "IsDynamicAmount" => 0,
    "OrderUserKey" => "1035712030",
    "OutOrderId" => "20250815103855825",
    "PayAmount" => "7.72",
    "PayTime" => "2025-08-15 10:41:18",
    "Signature" => "713e605f6ecdddb05fdb00708a32d229",
    "Status" => 1,
    "ToAddress" => "TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"
];

// 创建临时文件来模拟回调
$tempFile = tempnam(sys_get_temp_dir(), 'tokenpay_callback_test');
$postData = json_encode($testData);
file_put_contents($tempFile, $postData);

// 使用curl发送POST请求到回调处理器
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/epay/tokenpay_notify.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($postData)
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

echo "发送回调请求...\n";
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
if ($error) {
    echo "CURL错误: $error\n";
}
echo "响应内容: $response\n";

// 检查回调日志
$logFile = 'epay/tokenpay_callback.log';
if (file_exists($logFile)) {
    echo "\n=== 回调处理日志 ===\n";
    $logContent = file_get_contents($logFile);
    $lines = explode("\n", $logContent);
    // 显示最后20行日志
    $recentLines = array_slice($lines, -20);
    foreach ($recentLines as $line) {
        if (trim($line)) {
            echo $line . "\n";
        }
    }
} else {
    echo "\n回调日志文件不存在\n";
}

// 清理临时文件
unlink($tempFile);

echo "\n=== 测试完成 ===\n";
?>
