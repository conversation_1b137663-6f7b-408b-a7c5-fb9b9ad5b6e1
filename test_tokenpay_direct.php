<?php
echo "=== 直接测试TokenPay回调逻辑 ===\n";

// 模拟环境
$_POST = [];
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';

// 模拟TokenPay发送的回调数据
$testData = [
    "ActualAmount" => "20",
    "Amount" => "7.72", 
    "BaseCurrency" => "CNY",
    "BlockChainName" => "TRON",
    "BlockTransactionId" => "1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31",
    "Currency" => "TRX",
    "CurrencyName" => "TRX", 
    "FromAddress" => "TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS",
    "Id" => "689f0e41-c5ee-b4c5-0055-249046dc733c",
    "IsDynamicAmount" => 0,
    "OrderUserKey" => "1035712030",
    "OutOrderId" => "20250815103855825",
    "PayAmount" => "7.72",
    "PayTime" => "2025-08-15 10:41:18",
    "Signature" => "713e605f6ecdddb05fdb00708a32d229",
    "Status" => 1,
    "ToAddress" => "TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"
];

// 模拟php://input
$rawInput = json_encode($testData);

// 创建临时文件模拟php://input
$tempFile = tempnam(sys_get_temp_dir(), 'tokenpay_test');
file_put_contents($tempFile, $rawInput);

// 重定向php://input到临时文件
stream_wrapper_unregister('php');
stream_wrapper_register('php', 'TestInputWrapper');

class TestInputWrapper {
    public $context;
    private $position = 0;
    private static $data = '';
    
    public static function setData($data) {
        self::$data = $data;
    }
    
    public function stream_open($path, $mode, $options, &$opened_path) {
        if ($path === 'php://input') {
            $this->position = 0;
            return true;
        }
        return false;
    }
    
    public function stream_read($count) {
        $ret = substr(self::$data, $this->position, $count);
        $this->position += strlen($ret);
        return $ret;
    }
    
    public function stream_eof() {
        return $this->position >= strlen(self::$data);
    }
    
    public function stream_stat() {
        return array();
    }
}

TestInputWrapper::setData($rawInput);

// 包含配置
include_once("confing/common.php");

echo "配置检查:\n";
echo "- tokenpay_enabled: " . ($conf['tokenpay_enabled'] ?? '未设置') . "\n";
echo "- tokenpay_webhook_secret: " . ($conf['tokenpay_webhook_secret'] ?? '未设置') . "\n";

// 测试签名验证逻辑
$webhookSecret = $conf['tokenpay_webhook_secret'] ?? '';
$data = $testData;

echo "\n签名验证测试:\n";
$bodySignature = $data['Signature'];
echo "接收到的签名: $bodySignature\n";

$signData = $data;
unset($signData['Signature']);
ksort($signData);

$signStr = '';
foreach ($signData as $k => $v) {
    if ($v !== '' && $v !== null) {
        $signStr .= $k . '=' . $v . '&';
    }
}
$signStr = rtrim($signStr, '&') . $webhookSecret;
$calculatedSignature = md5($signStr);

echo "计算的签名: $calculatedSignature\n";
echo "签名验证: " . (hash_equals($calculatedSignature, $bodySignature) ? '通过' : '失败') . "\n";

// 测试字段映射
echo "\n字段映射测试:\n";
$orderNo = $data['OutOrderId'] ?? '';
$amount = $data['ActualAmount'] ?? $data['Amount'] ?? 0; // 订单金额
$payAmount = $data['PayAmount'] ?? $data['Amount'] ?? 0; // 实际支付的加密货币数量
$status = $data['Status'] ?? '';
$txId = $data['BlockTransactionId'] ?? '';
$coin = $data['Currency'] ?? '';

echo "- 订单号: $orderNo\n";
echo "- 订单金额: $amount\n";
echo "- 实际支付数量: $payAmount\n";
echo "- 状态: $status\n";
echo "- 交易哈希: $txId\n";
echo "- 币种: $coin\n";

// 状态转换
if (is_numeric($status)) {
    $status = (intval($status) === 1) ? 'success' : 'failed';
}
echo "- 转换后状态: $status\n";

// 检查订单是否存在
echo "\n订单检查:\n";
$srow = $DB->get_row("SELECT * FROM qingka_wangke_pay WHERE `out_trade_no`='".daddslashes($orderNo)."' LIMIT 1");
if ($srow) {
    echo "✓ 订单存在\n";
    echo "- 订单状态: {$srow['status']}\n";
    echo "- 订单金额: {$srow['money']}\n";
    echo "- 用户UID: {$srow['uid']}\n";
} else {
    echo "✗ 订单不存在\n";
}

// 测试金额比较
echo "\n金额比较测试:\n";
$expectedCents = (int)round(floatval($srow['money']) * 100);
$paidCents = (int)round(floatval($amount) * 100);
echo "- 期望金额(分): $expectedCents\n";
echo "- 实际金额(分): $paidCents\n";
echo "- 金额匹配: " . ($expectedCents === $paidCents ? '是' : '否') . "\n";

// 测试完整的入账逻辑
if (($status === 'success' || $status === 'completed') && $srow['status'] == 0 && $expectedCents === $paidCents) {
    echo "\n✓ 满足入账条件，可以处理订单\n";
} else {
    echo "\n✗ 不满足入账条件\n";
    echo "- 状态检查: " . ($status === 'success' || $status === 'completed' ? '通过' : '失败') . "\n";
    echo "- 订单状态检查: " . ($srow['status'] == 0 ? '通过' : '失败') . "\n";
    echo "- 金额检查: " . ($expectedCents === $paidCents ? '通过' : '失败') . "\n";
}

echo "\n=== 测试完成 ===\n";
?>
