<?php
include_once("confing/common.php");

echo "=== TokenPay 配置检查 ===\n";

// 检查数据库中的TokenPay配置
$configs = $DB->get_results("SELECT * FROM qingka_wangke_config WHERE v LIKE '%tokenpay%'");
echo "\n数据库中的TokenPay配置:\n";
foreach ($configs as $config) {
    echo "- {$config['v']}: {$config['k']}\n";
}

// 检查全局配置变量
echo "\n全局配置变量:\n";
echo "- tokenpay_enabled: " . (isset($conf['tokenpay_enabled']) ? $conf['tokenpay_enabled'] : '未设置') . "\n";
echo "- tokenpay_gateway: " . (isset($conf['tokenpay_gateway']) ? $conf['tokenpay_gateway'] : '未设置') . "\n";
echo "- tokenpay_webhook_secret: " . (isset($conf['tokenpay_webhook_secret']) ? $conf['tokenpay_webhook_secret'] : '未设置') . "\n";

// 检查TokenPay服务状态
echo "\n=== TokenPay 服务状态 ===\n";
$pidFile = 'epay/tokenpay/tokenpay.pid';
if (file_exists($pidFile)) {
    $pid = trim(file_get_contents($pidFile));
    echo "PID文件存在: $pid\n";
    
    // 检查进程是否运行
    $output = shell_exec("ps aux | grep $pid | grep -v grep");
    if ($output) {
        echo "TokenPay服务正在运行\n";
        echo "进程信息: " . trim($output) . "\n";
    } else {
        echo "TokenPay服务未运行\n";
    }
} else {
    echo "PID文件不存在\n";
}

// 检查配置文件
echo "\n=== TokenPay 配置文件 ===\n";
$configFile = 'epay/tokenpay/appsettings.json';
if (file_exists($configFile)) {
    echo "配置文件存在: $configFile\n";
    $configContent = file_get_contents($configFile);
    echo "配置文件内容:\n";
    echo $configContent . "\n";

    // 尝试解析JSON（去除注释）
    $cleanContent = preg_replace('/\/\/.*$/m', '', $configContent);
    $config = json_decode($cleanContent, true);
    if ($config) {
        echo "\n解析后的配置:\n";
        echo "- ApiToken: " . $config['ApiToken'] . "\n";
        echo "- WebSiteUrl: " . $config['WebSiteUrl'] . "\n";
        echo "- BaseCurrency: " . $config['BaseCurrency'] . "\n";
        echo "- ExpireTime: " . $config['ExpireTime'] . "\n";
        echo "- UseDynamicAddress: " . ($config['UseDynamicAddress'] ? 'true' : 'false') . "\n";
        echo "- OnlyConfirmed: " . ($config['OnlyConfirmed'] ? 'true' : 'false') . "\n";
        if (isset($config['Address']['TRON'])) {
            echo "- TRON收款地址: " . json_encode($config['Address']['TRON']) . "\n";
        }
        if (isset($config['Address']['EVM'])) {
            echo "- EVM收款地址: " . json_encode($config['Address']['EVM']) . "\n";
        }
        if (isset($config['Rate'])) {
            echo "- 汇率设置: TRX=" . $config['Rate']['TRX'] . ", USDT=" . $config['Rate']['USDT'] . "\n";
        }
    } else {
        echo "配置文件JSON解析失败\n";
    }
} else {
    echo "配置文件不存在: $configFile\n";
}

// 检查最近的支付记录
echo "\n=== 最近的加密货币支付记录 ===\n";
$recentPays = $DB->get_results("SELECT * FROM qingka_wangke_pay WHERE type='tokenpay' ORDER BY addtime DESC LIMIT 5");
if ($recentPays) {
    foreach ($recentPays as $pay) {
        echo "- 订单号: {$pay['out_trade_no']}, 金额: {$pay['money']}, 状态: {$pay['status']}, 时间: {$pay['addtime']}\n";
    }
} else {
    echo "没有找到加密货币支付记录\n";
}

// 检查最近的日志错误
echo "\n=== 最近的TokenPay日志错误 ===\n";
$logFile = 'epay/tokenpay/logs/log-' . date('Ymd') . '.log';
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $lines = explode("\n", $logContent);
    $errorLines = array_filter($lines, function($line) {
        return strpos($line, '签名或令牌校验失败') !== false ||
               strpos($line, 'Unauthorized') !== false ||
               strpos($line, '异步通知失败') !== false;
    });

    if ($errorLines) {
        echo "发现错误日志:\n";
        foreach (array_slice($errorLines, -5) as $line) {
            echo "- " . trim($line) . "\n";
        }
    } else {
        echo "没有发现相关错误日志\n";
    }
} else {
    echo "今日日志文件不存在: $logFile\n";
}

// 检查回调URL配置
echo "\n=== 回调URL配置检查 ===\n";
$scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$expectedCallbackUrl = $scheme . '://' . $host . '/epay/tokenpay_notify.php';
echo "期望的回调URL: $expectedCallbackUrl\n";

// 检查TokenPay配置中的ApiToken是否与数据库一致
$tokenpayConfig = json_decode(preg_replace('/\/\/.*$/m', '', file_get_contents('epay/tokenpay/appsettings.json')), true);
if ($tokenpayConfig && isset($tokenpayConfig['ApiToken'])) {
    $configApiToken = $tokenpayConfig['ApiToken'];
    $dbWebhookSecret = $conf['tokenpay_webhook_secret'] ?? '';

    echo "TokenPay配置中的ApiToken: $configApiToken\n";
    echo "数据库中的webhook_secret: $dbWebhookSecret\n";

    if ($configApiToken === $dbWebhookSecret) {
        echo "✓ ApiToken与webhook_secret一致\n";
    } else {
        echo "✗ ApiToken与webhook_secret不一致！这可能导致回调验证失败\n";
    }
}

echo "\n=== 检查完成 ===\n";
?>
