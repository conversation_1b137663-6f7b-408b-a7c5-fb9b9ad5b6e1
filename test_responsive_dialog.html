<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式弹窗测试</title>
    <!-- Element UI 样式 -->
    <link rel="stylesheet" href="../sxdk/element/index.css">
    <script src="./dk/vue.js"></script>
    <script src="../sxdk/element/index.js"></script>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .test-button { margin: 10px; }
        
        /* 响应式弹窗样式 */
        @media (max-width: 768px) {
            /* 移动端弹窗全屏显示 */
            .el-dialog {
                width: 95% !important;
                margin: 5vh auto !important;
                max-height: 90vh;
            }
            
            .el-dialog__body {
                padding: 15px !important;
                max-height: 70vh;
                overflow-y: auto;
            }
            
            /* 移动端支付方式按钮单列排列 */
            .pay-type-group {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .pay-type-group .el-radio {
                height: 45px;
                font-size: 16px;
            }

            .pay-type-group .el-radio__label {
                font-size: 16px;
            }
            
            /* 移动端字体调整 */
            .el-dialog__title {
                font-size: 16px !important;
            }
            
            .el-form-item__label {
                font-size: 14px !important;
            }
            
            /* 移动端按钮调整 */
            .dialog-footer .el-button {
                width: 45%;
                margin: 0 2.5%;
            }
        }
        
        @media (min-width: 769px) and (max-width: 1024px) {
            /* 平板端弹窗 */
            .el-dialog {
                width: 60% !important;
            }

            .pay-type-group {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .pay-type-group .el-radio {
                height: 42px;
            }
        }

        @media (min-width: 1025px) {
            /* 桌面端弹窗 */
            .el-dialog {
                width: 40% !important;
                min-width: 450px;
            }

            .pay-type-group {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .pay-type-group .el-radio {
                height: 40px;
            }
        }
        
        /* 支付方式按钮样式 */
        .pay-type-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            margin-bottom: 15px;
        }
        .pay-type-group .el-radio {
            margin: 0;
            width: 100%;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .pay-type-group .el-radio__input {
            margin-right: 8px;
        }

        .pay-type-group .el-radio__label {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
        }
        
        /* 推荐标签样式 */
        .recommend-tag {
            background: #F56C6C;
            color: white;
            font-size: 10px;
            padding: 1px 4px;
            border-radius: 8px;
            margin-left: 3px;
            font-weight: normal;
        }
        
        /* 支付金额显示优化 */
        .payment-amount {
            text-align: center;
            padding: 10px;
            background: #f5f7fa;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .payment-amount h3 {
            margin: 0;
            color: #409EFF;
            font-size: 24px;
        }
        
        @media (max-width: 768px) {
            .payment-amount h3 {
                font-size: 20px;
            }
        }
        
        .device-info {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
<div id="app" class="test-container">
    <h1>TokenPay充值弹窗响应式测试</h1>
    
    <div class="device-info">
        <h3>当前设备信息</h3>
        <p>屏幕宽度: <span id="screenWidth"></span>px</p>
        <p>设备类型: <span id="deviceType"></span></p>
        <p>用户代理: <span id="userAgent"></span></p>
    </div>
    
    <el-button type="primary" @click="showPayDialog" class="test-button">测试充值弹窗</el-button>
    <el-button @click="resizeTest" class="test-button">模拟屏幕变化</el-button>
    
    <!-- 支付方式弹窗 -->
    <el-dialog 
        title="选择支付方式" 
        :visible.sync="payDialogVisible" 
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="payDialogVisible = false"
        class="payment-dialog">
        <div class="payment-amount">
            <h3>¥{{money}}</h3>
            <p style="margin: 5px 0 0 0; color: #909399; font-size: 12px;">请选择支付方式</p>
        </div>
        
        <el-form>
            <div class="payment-methods">
                <el-radio-group v-model="payType" class="pay-type-group">
                    <el-radio label="tokenpay_trx" border>
                        <i class="el-icon-wallet"></i> TRX 
                        <span class="recommend-tag">推荐</span>
                    </el-radio>
                    <el-radio label="tokenpay_usdt" border>
                        <i class="el-icon-coin"></i> USDT
                    </el-radio>
                    <el-radio label="alipay" border>
                        <i class="el-icon-wallet"></i> 支付宝
                    </el-radio>
                    <el-radio label="wxpay" border>
                        <i class="el-icon-wechat"></i> 微信支付
                    </el-radio>
                </el-radio-group>
            </div>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="payDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitPay" :disabled="!payType">确 定</el-button>
        </span>
    </el-dialog>
</div>

<script>
new Vue({
    el: '#app',
    data: {
        payDialogVisible: false,
        payType: '',
        money: '100.00'
    },
    methods: {
        showPayDialog() {
            this.payDialogVisible = true;
        },
        submitPay() {
            this.$message.success('支付方式: ' + this.payType);
            this.payDialogVisible = false;
        },
        resizeTest() {
            this.updateDeviceInfo();
        },
        updateDeviceInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
            
            let deviceType = 'Desktop';
            if (window.innerWidth <= 768) {
                deviceType = 'Mobile';
            } else if (window.innerWidth <= 1024) {
                deviceType = 'Tablet';
            }
            document.getElementById('deviceType').textContent = deviceType;
            document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 50) + '...';
        }
    },
    mounted() {
        this.updateDeviceInfo();
        window.addEventListener('resize', this.updateDeviceInfo);
    }
});
</script>
</body>
</html>
