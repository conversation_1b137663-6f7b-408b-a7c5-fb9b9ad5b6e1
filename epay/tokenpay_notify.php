<?php
@header('Content-Type: application/json; charset=UTF-8');
include("../confing/common.php");

// 添加详细日志记录
function writeCallbackLog($message) {
    $logFile = dirname(__FILE__) . '/tokenpay_callback.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// 配置：开关与回调签名密钥
$enabled = isset($conf['tokenpay_enabled']) ? intval($conf['tokenpay_enabled']) : 0;
$webhookSecret = isset($conf['tokenpay_webhook_secret']) ? trim($conf['tokenpay_webhook_secret']) : '';

if ($enabled !== 1) {
    http_response_code(403);
    echo json_encode(["code"=>-1, "msg"=>"TokenPay 未开启"]);
    exit;
}

$raw = file_get_contents('php://input');
writeCallbackLog("收到回调请求，原始数据: " . $raw);

$data = json_decode($raw, true);
if (!is_array($data)) {
    writeCallbackLog("JSON解析失败");
    http_response_code(400);
    echo json_encode(["code"=>-1, "msg"=>"请求数据格式错误"]);
    exit;
}

writeCallbackLog("解析后的数据: " . json_encode($data, JSON_UNESCAPED_UNICODE));

// 校验回调（支持三种方式）：
// 1) TokenPay标准签名：Body中的Signature字段 = MD5(按ASCII排序的参数+ApiToken)
// 2) Header: X-TokenPay-Signature = HMAC-SHA256(raw, webhookSecret)
// 3) Body: token/apiToken 字段等于 webhookSecret（与 TokenPay 文档中的 ApiToken 一致）
$signature = isset($_SERVER['HTTP_X_TOKENPAY_SIGNATURE']) ? trim($_SERVER['HTTP_X_TOKENPAY_SIGNATURE']) : '';
$bodySignature = isset($data['Signature']) ? trim($data['Signature']) : '';
$bodyToken = '';
foreach (['token','Token','apiToken','ApiToken'] as $tk) {
    if (!empty($data[$tk])) { $bodyToken = trim($data[$tk]); break; }
}

if ($webhookSecret !== '') {
    $ok = false;
    writeCallbackLog("开始签名验证，webhook密钥: $webhookSecret");

    // TokenPay标准签名校验（优先）
    if (!$ok && $bodySignature !== '') {
        writeCallbackLog("尝试TokenPay标准签名验证，接收到的签名: $bodySignature");

        $signData = $data;
        unset($signData['Signature']); // 移除签名字段
        ksort($signData); // 按键名ASCII排序

        $signStr = '';
        foreach ($signData as $k => $v) {
            if ($v !== '' && $v !== null) {
                $signStr .= $k . '=' . $v . '&';
            }
        }
        $signStr = rtrim($signStr, '&') . $webhookSecret;
        $calculatedSignature = md5($signStr);

        writeCallbackLog("签名字符串: $signStr");
        writeCallbackLog("计算的签名: $calculatedSignature");

        $ok = hash_equals($calculatedSignature, $bodySignature);
        writeCallbackLog("TokenPay签名验证结果: " . ($ok ? '通过' : '失败'));
    }

    // HMAC 校验
    if (!$ok && $signature !== '') {
        writeCallbackLog("尝试HMAC签名验证");
        $calc = hash_hmac('sha256', $raw, $webhookSecret);
        $ok = hash_equals($calc, $signature);
        writeCallbackLog("HMAC签名验证结果: " . ($ok ? '通过' : '失败'));
    }

    // 明文 Token 校验（与 TokenPay ApiToken 对应）
    if (!$ok && $bodyToken !== '' && hash_equals($webhookSecret, $bodyToken)) {
        writeCallbackLog("明文Token验证通过");
        $ok = true;
    }

    if (!$ok) {
        writeCallbackLog("所有签名验证都失败");
        http_response_code(401);
        echo json_encode(["code"=>-1, "msg"=>"签名或令牌校验失败"]);
        exit;
    }

    writeCallbackLog("签名验证通过");
}

// 兼容不同字段命名（TokenPay使用OutOrderId, ActualAmount等字段）
$orderNo = $data['OutOrderId'] ?? $data['order_no'] ?? $data['orderNo'] ?? $data['client_order_no'] ?? '';
$amount  = $data['ActualAmount'] ?? $data['Amount'] ?? $data['amount'] ?? $data['payAmount'] ?? $data['money'] ?? 0; // 使用ActualAmount字段（订单金额）
$payAmount = $data['PayAmount'] ?? $data['Amount'] ?? 0; // 实际支付的加密货币数量
$status  = $data['Status'] ?? $data['status'] ?? $data['payStatus'] ?? ''; // TokenPay使用数字状态：1=成功
$txId    = $data['BlockTransactionId'] ?? $data['txid'] ?? $data['txId'] ?? $data['hash'] ?? '';
$coin    = $data['Currency'] ?? $data['coin'] ?? $data['token'] ?? '';

// TokenPay状态转换：1=成功，其他为失败
if (is_numeric($status)) {
    $status = (intval($status) === 1) ? 'success' : 'failed';
} else {
    $status = strtolower($status); // success/completed
}

if ($orderNo === '' || floatval($amount) <= 0) {
    http_response_code(400);
    echo json_encode(["code"=>-1, "msg"=>"缺少订单号或金额"]);
    exit;
}

// 加锁读取本地订单
$srow = $DB->get_row("SELECT * FROM qingka_wangke_pay WHERE `out_trade_no`='".daddslashes($orderNo)."' LIMIT 1 FOR UPDATE");
if (!$srow) {
    http_response_code(404);
    echo json_encode(["code"=>-1, "msg"=>"订单不存在"]);
    exit;
}
$userrow = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid='".$srow['uid']."'");

// 金额统一转换为分进行比较，避免浮点误差
$expectedCents = (int)round(floatval($srow['money']) * 100);
$paidCents     = (int)round(floatval($amount) * 100);

$date = date("Y-m-d H:i:s");

// 成功入账：状态正确、未处理、金额一致
writeCallbackLog("检查入账条件 - 状态: $status, 订单状态: {$srow['status']}, 期望金额: $expectedCents, 实际金额: $paidCents");

if (($status === 'success' || $status === 'completed') && $srow['status'] == 0 && $expectedCents === $paidCents) {
    writeCallbackLog("满足入账条件，开始处理订单");

    // 更新支付记录
    $DB->query("UPDATE `qingka_wangke_pay` SET `status`='1',`endtime`='".$date."',`trade_no`='".daddslashes($txId)."',`type`='tokenpay' WHERE `out_trade_no`='".daddslashes($orderNo)."'");

    // 赠送规则（加密货币充值专享：比传统支付多送2%）
    $money = floatval($srow['money']);
    $money3 = 0;
    if ($money >= 500) { $money3 = $money * 0.32; }      // 原30% + 2% = 32%
    elseif ($money >= 300) { $money3 = $money * 0.27; }  // 原25% + 2% = 27%
    elseif ($money >= 200) { $money3 = $money * 0.22; }  // 原20% + 2% = 22%
    elseif ($money >= 100) { $money3 = $money * 0.17; }  // 原15% + 2% = 17%
    elseif ($money >= 50)  { $money3 = $money * 0.12; }  // 原10% + 2% = 12%

    $mjyh = $DB->get_row("SELECT COUNT(*) as count FROM qingka_wangke_mijia WHERE uid='".$userrow['uid']."'");
    if ($mjyh && intval($mjyh['count']) > 0) { $money3 = 0; }

    // 入账余额
    $newBalance = floatval($userrow['money']) + $money + $money3;
    $DB->query("UPDATE `qingka_wangke_user` SET `money`='".$newBalance."', `zcz`=zcz+'".$money."' WHERE uid='".$userrow['uid']."'");

    // 写入日志
    // 对交易哈希进行脱敏处理，只显示前8位和后8位
    $maskedTxId = '';
    if (strlen($txId) > 16) {
        $maskedTxId = substr($txId, 0, 8) . '****' . substr($txId, -8);
    } else {
        $maskedTxId = $txId; // 如果哈希太短，保持原样
    }

    wlog($userrow['uid'], "在线充值", "用户[{$userrow['user']}]通过加密货币充值{$money}，币种：{$coin}，交易哈希：{$maskedTxId}", $money);
    if ($money3 > 0) {
        wlog($userrow['uid'], "充值赠送", "用户[{$userrow['user']}]加密货币充值专享赠送{$money3}（比传统支付多送2%）", $money3);
    }

    writeCallbackLog("订单处理成功 - 用户: {$userrow['user']}, 充值: $money, 赠送: $money3, 新余额: $newBalance");
    echo json_encode(["code"=>1, "msg"=>"success"]);
    exit;
} else {
    writeCallbackLog("不满足入账条件 - 状态检查: " . (($status === 'success' || $status === 'completed') ? '通过' : '失败') .
                    ", 订单状态检查: " . ($srow['status'] == 0 ? '通过' : '失败') .
                    ", 金额检查: " . ($expectedCents === $paidCents ? '通过' : '失败'));
}

// 幂等处理：不满足入账条件时，记录交易号与时间
$DB->query("UPDATE `qingka_wangke_pay` SET `endtime`='".$date."',`trade_no`='".daddslashes($txId)."' WHERE `out_trade_no`='".daddslashes($orderNo)."'");
echo json_encode(["code"=>1, "msg"=>"已忽略或已处理"]);
exit; 