[2025-08-15 11:35:27] 收到回调请求，原始数据: {"ActualAmount":"20","Amount":"7.72","BaseCurrency":"CNY","BlockChainName":"TRON","BlockTransactionId":"1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31","Currency":"TRX","CurrencyName":"TRX","FromAddress":"TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS","Id":"689f0e41-c5ee-b4c5-0055-249046dc733c","IsDynamicAmount":0,"OrderUserKey":"1035712030","OutOrderId":"20250815103855825","PayAmount":"7.72","PayTime":"2025-08-15 10:41:18","Signature":"713e605f6ecdddb05fdb00708a32d229","Status":1,"ToAddress":"TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"}
[2025-08-15 11:35:27] 解析后的数据: {"ActualAmount":"20","Amount":"7.72","BaseCurrency":"CNY","BlockChainName":"TRON","BlockTransactionId":"1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31","Currency":"TRX","CurrencyName":"TRX","FromAddress":"TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS","Id":"689f0e41-c5ee-b4c5-0055-249046dc733c","IsDynamicAmount":0,"OrderUserKey":"1035712030","OutOrderId":"20250815103855825","PayAmount":"7.72","PayTime":"2025-08-15 10:41:18","Signature":"713e605f6ecdddb05fdb00708a32d229","Status":1,"ToAddress":"TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"}
[2025-08-15 11:35:27] 开始签名验证，webhook密钥: pqo24uw3ja765skfhi42ah4654
[2025-08-15 11:35:27] 尝试TokenPay标准签名验证，接收到的签名: 713e605f6ecdddb05fdb00708a32d229
[2025-08-15 11:35:27] 签名字符串: ActualAmount=20&Amount=7.72&BaseCurrency=CNY&BlockChainName=TRON&BlockTransactionId=1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31&Currency=TRX&CurrencyName=TRX&FromAddress=TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS&Id=689f0e41-c5ee-b4c5-0055-249046dc733c&IsDynamicAmount=0&OrderUserKey=1035712030&OutOrderId=20250815103855825&PayAmount=7.72&PayTime=2025-08-15 10:41:18&Status=1&ToAddress=TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1jpqo24uw3ja765skfhi42ah4654
[2025-08-15 11:35:27] 计算的签名: 713e605f6ecdddb05fdb00708a32d229
[2025-08-15 11:35:27] TokenPay签名验证结果: 通过
[2025-08-15 11:35:27] 签名验证通过
[2025-08-15 11:35:27] 检查入账条件 - 状态: success, 订单状态: 0, 期望金额: 2000, 实际金额: 2000
[2025-08-15 11:35:27] 满足入账条件，开始处理订单
[2025-08-15 11:35:27] 订单处理成功 - 用户: 1035712030, 充值: 20, 赠送: 0, 新余额: 39.83
[2025-08-15 12:11:40] 收到回调请求，原始数据: {"ActualAmount":"20","Amount":"7.72","BaseCurrency":"CNY","BlockChainName":"TRON","BlockTransactionId":"1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31","Currency":"TRX","CurrencyName":"TRX","FromAddress":"TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS","Id":"689f0e41-c5ee-b4c5-0055-249046dc733c","IsDynamicAmount":0,"OrderUserKey":"1035712030","OutOrderId":"20250815103855825","PayAmount":"7.72","PayTime":"2025-08-15 10:41:18","Signature":"713e605f6ecdddb05fdb00708a32d229","Status":1,"ToAddress":"TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"}
[2025-08-15 12:11:40] 解析后的数据: {"ActualAmount":"20","Amount":"7.72","BaseCurrency":"CNY","BlockChainName":"TRON","BlockTransactionId":"1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31","Currency":"TRX","CurrencyName":"TRX","FromAddress":"TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS","Id":"689f0e41-c5ee-b4c5-0055-249046dc733c","IsDynamicAmount":0,"OrderUserKey":"1035712030","OutOrderId":"20250815103855825","PayAmount":"7.72","PayTime":"2025-08-15 10:41:18","Signature":"713e605f6ecdddb05fdb00708a32d229","Status":1,"ToAddress":"TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"}
[2025-08-15 12:11:40] 开始签名验证，webhook密钥: pqo24uw3ja765skfhi42ah4654
[2025-08-15 12:11:40] 尝试TokenPay标准签名验证，接收到的签名: 713e605f6ecdddb05fdb00708a32d229
[2025-08-15 12:11:40] 签名字符串: ActualAmount=20&Amount=7.72&BaseCurrency=CNY&BlockChainName=TRON&BlockTransactionId=1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31&Currency=TRX&CurrencyName=TRX&FromAddress=TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS&Id=689f0e41-c5ee-b4c5-0055-249046dc733c&IsDynamicAmount=0&OrderUserKey=1035712030&OutOrderId=20250815103855825&PayAmount=7.72&PayTime=2025-08-15 10:41:18&Status=1&ToAddress=TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1jpqo24uw3ja765skfhi42ah4654
[2025-08-15 12:11:40] 计算的签名: 713e605f6ecdddb05fdb00708a32d229
[2025-08-15 12:11:40] TokenPay签名验证结果: 通过
[2025-08-15 12:11:40] 签名验证通过
[2025-08-15 12:11:40] 检查入账条件 - 状态: success, 订单状态: 0, 期望金额: 2000, 实际金额: 2000
[2025-08-15 12:11:40] 满足入账条件，开始处理订单
[2025-08-15 12:11:40] 订单处理成功 - 用户: 1035712030, 充值: 20, 赠送: 0, 新余额: 19.83
[2025-08-15 12:11:42] 邮件发送结果: 邮件发送成功 | 模板:tokenpay_recharge | 收件人:<EMAIL>
