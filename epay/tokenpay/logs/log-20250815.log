2025-08-15 00:17:58.605 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 00:17:59.072 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.58
2025-08-15 00:17:59.080 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-15 00:17:59.086 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 01:17:58.602 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 01:17:58.968 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.57
2025-08-15 01:17:58.974 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-15 01:17:58.981 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 02:17:58.602 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 02:17:59.074 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.57
2025-08-15 02:17:59.081 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-15 02:17:59.085 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 03:17:58.601 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 03:17:58.993 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.58
2025-08-15 03:17:59.001 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-15 03:17:59.008 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 04:17:58.604 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 04:17:59.161 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.57
2025-08-15 04:17:59.171 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.19
2025-08-15 04:17:59.179 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 05:17:58.602 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 05:17:59.274 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.57
2025-08-15 05:17:59.281 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.19
2025-08-15 05:17:59.287 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 06:17:58.604 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 06:17:59.145 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.56
2025-08-15 06:17:59.153 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.19
2025-08-15 06:17:59.157 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 07:17:58.600 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 07:17:59.100 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.57
2025-08-15 07:17:59.107 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.19
2025-08-15 07:17:59.112 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 08:17:58.603 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 08:17:59.102 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.58
2025-08-15 08:17:59.108 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.19
2025-08-15 08:17:59.112 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 09:17:58.601 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 09:17:59.006 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.58
2025-08-15 09:17:59.013 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.19
2025-08-15 09:17:59.018 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 10:17:58.605 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 10:17:59.015 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.59
2025-08-15 10:17:59.022 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.19
2025-08-15 10:17:59.028 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-15 10:41:20.603 +08:00 [INF] 开始异步通知订单: "689f0e41-c5ee-b4c5-0055-249046dc733c"
2025-08-15 10:41:20.677 +08:00 [INF] 发起请求
URL：https://freedomp.icu/epay/tokenpay_notify.php
参数：{"ActualAmount":"20","Amount":"7.72","BaseCurrency":"CNY","BlockChainName":"TRON","BlockTransactionId":"1fafa60d2025-08-15 10:42:21.067 +08:00 [INF] 开始异步通知订单: "689f0e41-c5ee-b4c5-0055-249046dc733c"
2025-08-15 10:42:21.278 +08:00 [INF] 发起请求
URL：https://freedomp.icu/epay/tokenpay_notify.php
参数：{"ActualAmount":"20","Amount":"7.72","BaseCurrency":"CNY","BlockChainName":"TRON","BlockTransactionId":"1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31","Currency":"TRX","CurrencyName":"TRX","FromAddress":"TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS","Id":"689f0e41-c5ee-b4c5-0055-249046dc733c","IsDynamicAmount":0,"OrderUserKey":"1035712030","OutOrderId":"20250815103855825","PayAmount":"7.72","PayTime":"2025-08-15 10:41:18","Signature":"713e605f6ecdddb05fdb00708a32d229","Status":1,"ToAddress":"TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"}
2025-08-15 10:42:21.351 +08:00 [INF] 收到响应
URL：https://freedomp.icu/epay/tokenpay_notify.php
响应：{"code":-1,"msg":"\u7b7e\u540d\u6216\u4ee4\u724c\u6821\u9a8c\u5931\u8d25"}
2025-08-15 10:42:21.353 +08:00 [INF] 订单异步通知失败：Call failed with status code 401 (Unauthorized): POST https://freedomp.icu/epay/tokenpay_notify.php
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): POST https://freedomp.icu/epay/tokenpay_notify.php
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, Ht2025-08-15 11:17:58.604 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 11:17:59.095 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.6
2025-08-15 11:17:59.102 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.19
2025-08-15 11:17:59.108 +08:00 [INF] ------------------结束更新汇率------------------
1-c5ee-b4c5-0055-249046dc733c"
2025-08-15 10:43:22.074 +08:00 [INF] 发起请求
URL：https://freedomp.icu/epay/tokenpay_notify.php
参数：{"ActualAmount":"20","Amount":"7.72","BaseCurrency":"CNY","BlockChainName":"TRON","BlockTransactionId":"1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31","Currency":"TRX","CurrencyName":"TRX","FromAddress":"TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS","Id":"689f0e41-c5ee-b4c5-0055-249046dc733c","IsDynamicAmount":0,"OrderUserKey":"1035712030","OutOrderId":"20250815103855825","PayAmount":"7.72","PayTime":"2025-08-15 10:41:18","Signature":"713e605f6ecdddb05fdb00708a32d229","Status":1,"ToAddress":"TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"}
2025-08-15 10:43:22.107 +08:00 [INF] 收到响应
URL：https://freedomp.icu/epay/tokenpay_notify.php
响应：{"code":-1,"msg":"\u7b7e\u540d\u6216\u4ee4\u724c\u6821\u9a8c\u5931\u8d25"}
2025-08-15 10:43:22.107 +08:00 [INF] 订单异步通知失败：Call failed with status code 401 (Unauthorized): POST https://freedomp.icu/epay/tokenpay_notify.php
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): POST https://freedomp.icu/epay/tokenpay_notify.php
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at TokenPay.BgServices.OrderNotifyService.Notify(TokenOrders order)
2025-08-15 10:43:22.108 +08:00 [INF] 订单: "689f0e41-c5ee-b4c5-0055-249046dc733c"，通知结果：失败
2025-08-15 11:18:18.062 +08:00 [INF] 订单["689f106a-c5ee-b4c5-0055-24913d689bbb"]过期了！
2025-08-15 11:21:38.067 +08:00 [INF] 订单["689f1135-c5ee-b4c5-0055-249235b7666c"]过期了！
2025-08-15 11:25:18.067 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-15 11:25:18.286 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.59
2025-08-15 11:25:18.295 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.19
2025-08-15 11:25:18.302 +08:00 [INF] ------------------结束更新汇率------------------
