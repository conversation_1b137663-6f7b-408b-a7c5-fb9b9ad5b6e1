<?php
echo "=== 测试支付状态检查 ===\n";

include_once("confing/common.php");

// 检查最近的支付订单状态
echo "最近的支付订单:\n";
$orders = $DB->get_results("SELECT * FROM qingka_wangke_pay ORDER BY addtime DESC LIMIT 10");

foreach ($orders as $order) {
    $statusText = $order['status'] == 1 ? '已支付' : '未支付';
    $typeText = $order['type'] ?: '传统支付';
    echo "- 订单号: {$order['out_trade_no']}, 金额: {$order['money']}, 状态: $statusText, 类型: $typeText, 时间: {$order['addtime']}\n";
}

// 检查特定的TokenPay订单
$testOrderNo = "20250815103855825";
echo "\n检查测试订单 $testOrderNo:\n";
$testOrder = $DB->get_row("SELECT * FROM qingka_wangke_pay WHERE out_trade_no='$testOrderNo'");

if ($testOrder) {
    echo "- 订单存在\n";
    echo "- 状态: " . ($testOrder['status'] == 1 ? '已支付' : '未支付') . "\n";
    echo "- 金额: {$testOrder['money']}\n";
    echo "- 类型: " . ($testOrder['type'] ?: '未设置') . "\n";
    echo "- 交易号: " . ($testOrder['trade_no'] ?: '无') . "\n";
    echo "- 结束时间: " . ($testOrder['endtime'] ?: '无') . "\n";
    echo "- 用户UID: {$testOrder['uid']}\n";
    
    // 检查用户信息
    $user = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid='{$testOrder['uid']}'");
    if ($user) {
        echo "- 用户: {$user['user']}\n";
        echo "- 当前余额: {$user['money']}\n";
        echo "- 累计充值: {$user['zcz']}\n";
    }
} else {
    echo "- 订单不存在\n";
}

// 检查用户日志
echo "\n检查用户充值日志:\n";
if (isset($user)) {
    $logs = $DB->get_results("SELECT * FROM qingka_wangke_log WHERE uid='{$user['uid']}' AND action LIKE '%充值%' ORDER BY addtime DESC LIMIT 5");
    foreach ($logs as $log) {
        echo "- {$log['addtime']}: {$log['action']} - {$log['remark']} (金额: {$log['money']})\n";
    }
}

echo "\n=== 测试完成 ===\n";
?>
