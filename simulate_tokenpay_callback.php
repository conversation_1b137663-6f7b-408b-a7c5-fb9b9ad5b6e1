<?php
echo "=== 模拟TokenPay回调处理 ===\n";

include_once("confing/common.php");

// 模拟TokenPay发送的回调数据
$testData = [
    "ActualAmount" => "20",
    "Amount" => "7.72", 
    "BaseCurrency" => "CNY",
    "BlockChainName" => "TRON",
    "BlockTransactionId" => "1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31",
    "Currency" => "TRX",
    "CurrencyName" => "TRX", 
    "FromAddress" => "TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS",
    "Id" => "689f0e41-c5ee-b4c5-0055-249046dc733c",
    "IsDynamicAmount" => 0,
    "OrderUserKey" => "1035712030",
    "OutOrderId" => "20250815103855825",
    "PayAmount" => "7.72",
    "PayTime" => "2025-08-15 10:41:18",
    "Signature" => "713e605f6ecdddb05fdb00708a32d229",
    "Status" => 1,
    "ToAddress" => "TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"
];

$raw = json_encode($testData);
$data = $testData;

// 配置检查
$enabled = isset($conf['tokenpay_enabled']) ? intval($conf['tokenpay_enabled']) : 0;
$webhookSecret = isset($conf['tokenpay_webhook_secret']) ? trim($conf['tokenpay_webhook_secret']) : '';

echo "配置检查:\n";
echo "- TokenPay启用: " . ($enabled === 1 ? '是' : '否') . "\n";
echo "- Webhook密钥: $webhookSecret\n";

if ($enabled !== 1) {
    echo "✗ TokenPay未启用\n";
    exit;
}

// 签名验证
$bodySignature = isset($data['Signature']) ? trim($data['Signature']) : '';
echo "\n签名验证:\n";
echo "- 接收到的签名: $bodySignature\n";

if ($webhookSecret !== '' && $bodySignature !== '') {
    $signData = $data;
    unset($signData['Signature']);
    ksort($signData);
    
    $signStr = '';
    foreach ($signData as $k => $v) {
        if ($v !== '' && $v !== null) {
            $signStr .= $k . '=' . $v . '&';
        }
    }
    $signStr = rtrim($signStr, '&') . $webhookSecret;
    $calculatedSignature = md5($signStr);
    
    echo "- 计算的签名: $calculatedSignature\n";
    $signatureValid = hash_equals($calculatedSignature, $bodySignature);
    echo "- 签名验证: " . ($signatureValid ? '通过' : '失败') . "\n";
    
    if (!$signatureValid) {
        echo "✗ 签名验证失败\n";
        exit;
    }
} else {
    echo "- 跳过签名验证（缺少必要参数）\n";
}

// 字段解析
$orderNo = $data['OutOrderId'] ?? '';
$amount = $data['ActualAmount'] ?? 0;
$status = $data['Status'] ?? '';
$txId = $data['BlockTransactionId'] ?? '';
$coin = $data['Currency'] ?? '';

echo "\n字段解析:\n";
echo "- 订单号: $orderNo\n";
echo "- 金额: $amount\n";
echo "- 状态: $status\n";
echo "- 交易哈希: $txId\n";
echo "- 币种: $coin\n";

// 状态转换
if (is_numeric($status)) {
    $status = (intval($status) === 1) ? 'success' : 'failed';
}
echo "- 转换后状态: $status\n";

if ($orderNo === '' || floatval($amount) <= 0) {
    echo "✗ 缺少订单号或金额\n";
    exit;
}

// 查询订单
echo "\n订单查询:\n";
$srow = $DB->get_row("SELECT * FROM qingka_wangke_pay WHERE `out_trade_no`='".daddslashes($orderNo)."' LIMIT 1");
if (!$srow) {
    echo "✗ 订单不存在\n";
    exit;
}

echo "- 订单存在: 是\n";
echo "- 订单状态: {$srow['status']}\n";
echo "- 订单金额: {$srow['money']}\n";
echo "- 用户UID: {$srow['uid']}\n";

$userrow = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid='".$srow['uid']."'");
echo "- 用户: {$userrow['user']}\n";

// 金额验证
$expectedCents = (int)round(floatval($srow['money']) * 100);
$paidCents = (int)round(floatval($amount) * 100);

echo "\n金额验证:\n";
echo "- 期望金额(分): $expectedCents\n";
echo "- 实际金额(分): $paidCents\n";
echo "- 金额匹配: " . ($expectedCents === $paidCents ? '是' : '否') . "\n";

// 入账条件检查
$canProcess = ($status === 'success' || $status === 'completed') && $srow['status'] == 0 && $expectedCents === $paidCents;

echo "\n入账条件检查:\n";
echo "- 支付成功: " . (($status === 'success' || $status === 'completed') ? '是' : '否') . "\n";
echo "- 订单未处理: " . ($srow['status'] == 0 ? '是' : '否') . "\n";
echo "- 金额一致: " . ($expectedCents === $paidCents ? '是' : '否') . "\n";
echo "- 可以入账: " . ($canProcess ? '是' : '否') . "\n";

if ($canProcess) {
    echo "\n✓ 所有条件满足，可以处理入账\n";
    echo "注意：这是模拟测试，不会实际修改数据库\n";
} else {
    echo "\n✗ 不满足入账条件\n";
}

echo "\n=== 模拟完成 ===\n";
?>
