<?php
include_once("confing/common.php");

echo "=== TokenPay 签名验证调试 ===\n";

// 从日志中提取的实际回调数据
$testData = [
    "ActualAmount" => "20",
    "Amount" => "7.72", 
    "BaseCurrency" => "CNY",
    "BlockChainName" => "TRON",
    "BlockTransactionId" => "1fafa60d6065c1eb6991d2bad0792fab632c4c2fdae62fa7e3681e2ef53f8b31",
    "Currency" => "TRX",
    "CurrencyName" => "TRX", 
    "FromAddress" => "TX1MZujUw3UTZ7nQwKXw5HTEcQFnesDFbS",
    "Id" => "689f0e41-c5ee-b4c5-0055-249046dc733c",
    "IsDynamicAmount" => 0,
    "OrderUserKey" => "1035712030",
    "OutOrderId" => "20250815103855825",
    "PayAmount" => "7.72",
    "PayTime" => "2025-08-15 10:41:18",
    "Signature" => "713e605f6ecdddb05fdb00708a32d229",
    "Status" => 1,
    "ToAddress" => "TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j"
];

$webhookSecret = $conf['tokenpay_webhook_secret'] ?? '';
echo "Webhook Secret: $webhookSecret\n";

// 测试不同的签名验证方法

// 方法1: 直接比较Signature字段与webhookSecret
echo "\n方法1: 直接比较Signature字段\n";
$receivedSignature = $testData['Signature'];
echo "接收到的签名: $receivedSignature\n";
echo "是否匹配: " . ($receivedSignature === $webhookSecret ? '是' : '否') . "\n";

// 方法2: 按照TokenPay文档，可能需要排除Signature字段后计算MD5
echo "\n方法2: 排除Signature字段后按ASCII排序计算MD5\n";
$signData = $testData;
unset($signData['Signature']); // 移除签名字段
ksort($signData); // 按键名排序

$signStr = '';
foreach ($signData as $k => $v) {
    if ($v !== '' && $v !== null) {
        $signStr .= $k . '=' . $v . '&';
    }
}
$signStr = rtrim($signStr, '&') . $webhookSecret;
$calculatedSignature = md5($signStr);

echo "签名字符串: $signStr\n";
echo "计算的签名: $calculatedSignature\n";
echo "接收的签名: $receivedSignature\n";
echo "是否匹配: " . ($calculatedSignature === $receivedSignature ? '是' : '否') . "\n";

// 方法3: 尝试不同的签名字符串格式
echo "\n方法3: 尝试不同格式\n";
$signStr2 = '';
foreach ($signData as $k => $v) {
    $signStr2 .= $k . $v;
}
$signStr2 .= $webhookSecret;
$calculatedSignature2 = md5($signStr2);
echo "签名字符串2: $signStr2\n";
echo "计算的签名2: $calculatedSignature2\n";
echo "是否匹配: " . ($calculatedSignature2 === $receivedSignature ? '是' : '否') . "\n";

// 方法4: 检查是否是HMAC-SHA256
echo "\n方法4: HMAC-SHA256验证\n";
$rawData = json_encode($testData);
$hmacSignature = hash_hmac('sha256', $rawData, $webhookSecret);
echo "HMAC-SHA256签名: $hmacSignature\n";
echo "是否匹配: " . ($hmacSignature === $receivedSignature ? '是' : '否') . "\n";

echo "\n=== 调试完成 ===\n";
?>
